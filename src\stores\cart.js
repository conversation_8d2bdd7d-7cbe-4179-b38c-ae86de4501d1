import { defineStore } from 'pinia'

export const useCartStore = defineStore('cart', {
  state: () => ({
    items: [],
    isOpen: false,
    loading: false
  }),

  getters: {
    totalItems: (state) => {
      return state.items.reduce((total, item) => total + item.quantity, 0)
    },

    totalPrice: (state) => {
      return state.items.reduce((total, item) => total + (item.price * item.quantity), 0)
    },

    formattedTotalPrice: (state) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(state.totalPrice)
    },

    itemCount: (state) => state.items.length,

    isEmpty: (state) => state.items.length === 0,

    getItemById: (state) => {
      return (id) => state.items.find(item => item.id === id)
    },

    getItemQuantity: (state) => {
      return (id) => {
        const item = state.items.find(item => item.id === id)
        return item ? item.quantity : 0
      }
    }
  },

  actions: {
    addItem(product, selectedSize = null, selectedColor = null, quantity = 1) {
      const existingItem = this.items.find(item => 
        item.id === product.id && 
        item.selectedSize === selectedSize && 
        item.selectedColor === selectedColor
      )

      if (existingItem) {
        existingItem.quantity += quantity
      } else {
        this.items.push({
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.image,
          selectedSize,
          selectedColor,
          quantity,
          addedAt: new Date()
        })
      }

      // Show success animation
      this.showCartAnimation()
    },

    removeItem(itemId, selectedSize = null, selectedColor = null) {
      const index = this.items.findIndex(item => 
        item.id === itemId && 
        item.selectedSize === selectedSize && 
        item.selectedColor === selectedColor
      )
      
      if (index > -1) {
        this.items.splice(index, 1)
      }
    },

    updateQuantity(itemId, selectedSize, selectedColor, quantity) {
      const item = this.items.find(item => 
        item.id === itemId && 
        item.selectedSize === selectedSize && 
        item.selectedColor === selectedColor
      )
      
      if (item) {
        if (quantity <= 0) {
          this.removeItem(itemId, selectedSize, selectedColor)
        } else {
          item.quantity = quantity
        }
      }
    },

    increaseQuantity(itemId, selectedSize = null, selectedColor = null) {
      const item = this.items.find(item => 
        item.id === itemId && 
        item.selectedSize === selectedSize && 
        item.selectedColor === selectedColor
      )
      
      if (item) {
        item.quantity += 1
      }
    },

    decreaseQuantity(itemId, selectedSize = null, selectedColor = null) {
      const item = this.items.find(item => 
        item.id === itemId && 
        item.selectedSize === selectedSize && 
        item.selectedColor === selectedColor
      )
      
      if (item) {
        if (item.quantity > 1) {
          item.quantity -= 1
        } else {
          this.removeItem(itemId, selectedSize, selectedColor)
        }
      }
    },

    clearCart() {
      this.items = []
    },

    toggleCart() {
      this.isOpen = !this.isOpen
    },

    openCart() {
      this.isOpen = true
    },

    closeCart() {
      this.isOpen = false
    },

    showCartAnimation() {
      // Trigger cart bounce animation
      const cartElement = document.querySelector('.cart-icon')
      if (cartElement) {
        cartElement.classList.add('animate-bounce-in')
        setTimeout(() => {
          cartElement.classList.remove('animate-bounce-in')
        }, 600)
      }
    },

    setLoading(loading) {
      this.loading = loading
    },

    // Simulate checkout process
    async checkout() {
      this.setLoading(true)
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Clear cart after successful checkout
        this.clearCart()
        this.closeCart()
        
        return { success: true, message: 'Order placed successfully!' }
      } catch (error) {
        return { success: false, message: 'Checkout failed. Please try again.' }
      } finally {
        this.setLoading(false)
      }
    },

    // Save cart to localStorage
    saveToStorage() {
      localStorage.setItem('kayisdagi-cart', JSON.stringify(this.items))
    },

    // Load cart from localStorage
    loadFromStorage() {
      const saved = localStorage.getItem('kayisdagi-cart')
      if (saved) {
        try {
          this.items = JSON.parse(saved)
        } catch (error) {
          console.error('Failed to load cart from storage:', error)
          this.items = []
        }
      }
    }
  }
})

// Auto-save cart to localStorage when items change
export const setupCartPersistence = (cartStore) => {
  cartStore.$subscribe((mutation, state) => {
    cartStore.saveToStorage()
  })
}
