@tailwind base;
@tailwind components;
@tailwind utilities;

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Custom Base Styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-gray-900 bg-white;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }
}

/* Custom Components */
@layer components {
  .btn-primary {
    @apply bg-primary-900 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:bg-primary-800 hover:shadow-lg transform hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply bg-white text-primary-900 border-2 border-primary-900 px-6 py-3 rounded-lg font-medium transition-all duration-300 hover:bg-primary-900 hover:text-white hover:shadow-lg transform hover:-translate-y-0.5;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  .nav-link {
    @apply text-gray-700 hover:text-primary-900 font-medium transition-colors duration-200 relative;
  }
  
  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-primary-900 transition-all duration-300;
  }
  
  .nav-link:hover::after {
    @apply w-full;
  }
  
  .hero-gradient {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }
  
  .product-card {
    @apply card overflow-hidden cursor-pointer;
  }
  
  .product-card img {
    @apply transition-transform duration-500;
  }

  .product-card:hover img {
    @apply scale-110;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-4 border-gray-200 border-t-primary-900;
  }
}

/* Custom Animations */
@layer utilities {
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
  
  .animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out forwards;
  }
  
  .animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out forwards;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.8s ease-out forwards;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out forwards;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out forwards;
  }
}

/* Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scroll animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.scroll-animate.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Mobile menu animations */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease;
}

.mobile-menu-enter-from {
  opacity: 0;
  transform: translateX(-100%);
}

.mobile-menu-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

/* Cart animations */
.cart-item-enter-active,
.cart-item-leave-active {
  transition: all 0.3s ease;
}

.cart-item-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.cart-item-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* Page transitions */
.page-enter-active,
.page-leave-active {
  transition: all 0.4s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-600;
}
