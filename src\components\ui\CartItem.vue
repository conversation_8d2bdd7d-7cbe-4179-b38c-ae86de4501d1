<template>
  <div class="cart-item flex flex-col sm:flex-row gap-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200">
    <!-- Product Image -->
    <div class="flex-shrink-0">
      <img
        :src="item.image"
        :alt="item.name"
        class="w-24 h-24 sm:w-32 sm:h-32 object-cover rounded-lg"
      />
    </div>

    <!-- Product Details -->
    <div class="flex-1 min-w-0">
      <div class="flex flex-col sm:flex-row sm:justify-between gap-4">
        <!-- Product Info -->
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-primary-900 mb-1">
            {{ item.name }}
          </h3>
          
          <!-- Variants -->
          <div class="flex flex-wrap gap-2 mb-2">
            <span v-if="item.selectedSize" class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
              Size: {{ item.selectedSize }}
            </span>
            <span v-if="item.selectedColor" class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
              Color: {{ item.selectedColor }}
            </span>
          </div>

          <!-- Price -->
          <div class="flex items-center gap-2 mb-3">
            <span class="text-xl font-bold text-primary-900">
              ${{ item.price.toFixed(2) }}
            </span>
            <span class="text-sm text-gray-500">each</span>
          </div>

          <!-- Mobile Quantity and Remove (shown on small screens) -->
          <div class="flex items-center justify-between sm:hidden">
            <!-- Quantity Controls -->
            <div class="flex items-center border border-gray-300 rounded-lg">
              <button
                @click="decreaseQuantity"
                :disabled="item.quantity <= 1 || updating"
                class="px-3 py-2 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <MinusIcon class="w-4 h-4" />
              </button>
              <span class="px-4 py-2 border-x border-gray-300 min-w-[3rem] text-center">
                {{ item.quantity }}
              </span>
              <button
                @click="increaseQuantity"
                :disabled="updating"
                class="px-3 py-2 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PlusIcon class="w-4 h-4" />
              </button>
            </div>

            <!-- Remove Button -->
            <button
              @click="removeFromCart"
              :disabled="removing"
              class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200 disabled:opacity-50"
              title="Remove item"
            >
              <TrashIcon class="w-5 h-5" />
            </button>
          </div>
        </div>

        <!-- Desktop Controls (hidden on mobile) -->
        <div class="hidden sm:flex sm:flex-col sm:items-end sm:justify-between">
          <!-- Quantity Controls -->
          <div class="flex items-center border border-gray-300 rounded-lg mb-4">
            <button
              @click="decreaseQuantity"
              :disabled="item.quantity <= 1 || updating"
              class="px-3 py-2 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <MinusIcon class="w-4 h-4" />
            </button>
            <span class="px-4 py-2 border-x border-gray-300 min-w-[3rem] text-center">
              {{ item.quantity }}
            </span>
            <button
              @click="increaseQuantity"
              :disabled="updating"
              class="px-3 py-2 hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PlusIcon class="w-4 h-4" />
            </button>
          </div>

          <!-- Remove Button -->
          <button
            @click="removeFromCart"
            :disabled="removing"
            class="flex items-center gap-2 px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200 disabled:opacity-50"
          >
            <TrashIcon class="w-4 h-4" />
            <span class="text-sm font-medium">Remove</span>
          </button>
        </div>
      </div>

      <!-- Subtotal -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-100">
        <span class="text-sm text-gray-600">Subtotal:</span>
        <span class="text-lg font-bold text-primary-900">
          ${{ subtotal.toFixed(2) }}
        </span>
      </div>

      <!-- Added Date -->
      <div class="mt-2">
        <span class="text-xs text-gray-500">
          Added {{ formatDate(item.addedAt) }}
        </span>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div v-if="updating || removing" class="absolute inset-0 bg-white/50 flex items-center justify-center rounded-lg">
      <div class="flex items-center gap-2 text-primary-600">
        <div class="w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
        <span class="text-sm font-medium">
          {{ updating ? 'Updating...' : 'Removing...' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  MinusIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update-quantity', 'remove-item'])

// Reactive data
const updating = ref(false)
const removing = ref(false)

// Computed properties
const subtotal = computed(() => {
  return props.item.price * props.item.quantity
})

// Methods
const increaseQuantity = async () => {
  if (updating.value) return
  
  updating.value = true
  
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    emit('update-quantity', 
      props.item.id, 
      props.item.selectedSize, 
      props.item.selectedColor, 
      props.item.quantity + 1
    )
  } catch (error) {
    console.error('Failed to update quantity:', error)
  } finally {
    updating.value = false
  }
}

const decreaseQuantity = async () => {
  if (updating.value || props.item.quantity <= 1) return
  
  updating.value = true
  
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    emit('update-quantity', 
      props.item.id, 
      props.item.selectedSize, 
      props.item.selectedColor, 
      props.item.quantity - 1
    )
  } catch (error) {
    console.error('Failed to update quantity:', error)
  } finally {
    updating.value = false
  }
}

const removeFromCart = async () => {
  if (removing.value) return
  
  // Confirm removal
  if (!confirm('Are you sure you want to remove this item from your cart?')) {
    return
  }
  
  removing.value = true
  
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    emit('remove-item', 
      props.item.id, 
      props.item.selectedSize, 
      props.item.selectedColor
    )
  } catch (error) {
    console.error('Failed to remove item:', error)
    removing.value = false
  }
}

const formatDate = (date) => {
  const now = new Date()
  const itemDate = new Date(date)
  const diffInHours = Math.floor((now - itemDate) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}
</script>

<style scoped>
.cart-item {
  position: relative;
}

.cart-item:hover {
  border-color: #e5e7eb;
}
</style>
