<template>
  <footer class="bg-primary-900 text-white">
    <!-- Main Footer Content -->
    <div class="container mx-auto px-4 py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <span class="text-primary-900 font-bold text-xl">K</span>
            </div>
            <div>
              <h3 class="text-xl font-display font-bold">Kayışdağı Store</h3>
              <p class="text-primary-200 text-sm">Premium Men's Fashion</p>
            </div>
          </div>
          <p class="text-primary-200 leading-relaxed">
            Discover premium men's clothing that combines quality, style, and sophistication. 
            Your destination for modern masculine fashion.
          </p>
          
          <!-- Social Media -->
          <div class="flex space-x-4">
            <a
              v-for="social in socialLinks"
              :key="social.name"
              :href="social.url"
              :title="social.name"
              class="w-10 h-10 bg-primary-800 rounded-lg flex items-center justify-center hover:bg-primary-700 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              <component :is="social.icon" class="w-5 h-5" />
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="space-y-4">
          <h4 class="text-lg font-semibold">Quick Links</h4>
          <nav class="space-y-2">
            <router-link
              v-for="link in quickLinks"
              :key="link.name"
              :to="link.path"
              class="block text-primary-200 hover:text-white transition-colors"
            >
              {{ link.name }}
            </router-link>
          </nav>
        </div>

        <!-- Categories -->
        <div class="space-y-4">
          <h4 class="text-lg font-semibold">Categories</h4>
          <nav class="space-y-2">
            <router-link
              v-for="category in categories.filter(c => c.id !== 'all')"
              :key="category.id"
              :to="`/products/${category.id}`"
              class="block text-primary-200 hover:text-white transition-colors"
            >
              {{ category.name }}
            </router-link>
          </nav>
        </div>

        <!-- Contact Info -->
        <div class="space-y-4">
          <h4 class="text-lg font-semibold">Contact Us</h4>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <MapPinIcon class="w-5 h-5 text-primary-300 mt-0.5 flex-shrink-0" />
              <div>
                <p class="text-primary-200">123 Fashion Street</p>
                <p class="text-primary-200">Kayışdağı, Istanbul, Turkey</p>
              </div>
            </div>
            
            <div class="flex items-center space-x-3">
              <PhoneIcon class="w-5 h-5 text-primary-300 flex-shrink-0" />
              <a href="tel:+905551234567" class="text-primary-200 hover:text-white transition-colors">
                +90 555 123 45 67
              </a>
            </div>
            
            <div class="flex items-center space-x-3">
              <EnvelopeIcon class="w-5 h-5 text-primary-300 flex-shrink-0" />
              <a href="mailto:<EMAIL>" class="text-primary-200 hover:text-white transition-colors">
                <EMAIL>
              </a>
            </div>
          </div>

          <!-- Store Hours -->
          <div class="mt-6">
            <h5 class="font-semibold mb-2">Store Hours</h5>
            <div class="text-primary-200 text-sm space-y-1">
              <p>Monday - Friday: 9:00 AM - 9:00 PM</p>
              <p>Saturday: 10:00 AM - 8:00 PM</p>
              <p>Sunday: 12:00 PM - 6:00 PM</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Newsletter Section -->
    <div class="border-t border-primary-800">
      <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto text-center">
          <h3 class="text-xl font-semibold mb-2">Stay in Style</h3>
          <p class="text-primary-200 mb-6">
            Subscribe to our newsletter for exclusive offers, style tips, and new arrivals.
          </p>
          
          <form @submit.prevent="subscribeNewsletter" class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              v-model="email"
              type="email"
              placeholder="Enter your email"
              class="flex-1 px-4 py-3 rounded-lg bg-primary-800 border border-primary-700 text-white placeholder-primary-300 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent"
              required
            >
            <button
              type="submit"
              :disabled="subscribing"
              class="px-6 py-3 bg-white text-primary-900 font-semibold rounded-lg hover:bg-primary-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ subscribing ? 'Subscribing...' : 'Subscribe' }}
            </button>
          </form>
          
          <p v-if="subscriptionMessage" class="mt-4 text-sm" :class="subscriptionSuccess ? 'text-green-300' : 'text-red-300'">
            {{ subscriptionMessage }}
          </p>
        </div>
      </div>
    </div>

    <!-- Bottom Footer -->
    <div class="border-t border-primary-800">
      <div class="container mx-auto px-4 py-6">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <!-- Copyright -->
          <div class="text-primary-200 text-sm">
            <p>&copy; {{ currentYear }} Kayışdağı Store. All rights reserved.</p>
          </div>

          <!-- Legal Links -->
          <div class="flex space-x-6 text-sm">
            <a href="#" class="text-primary-200 hover:text-white transition-colors">Privacy Policy</a>
            <a href="#" class="text-primary-200 hover:text-white transition-colors">Terms of Service</a>
            <a href="#" class="text-primary-200 hover:text-white transition-colors">Return Policy</a>
          </div>

          <!-- Payment Methods -->
          <div class="flex items-center space-x-2">
            <span class="text-primary-200 text-sm mr-2">We accept:</span>
            <div class="flex space-x-2">
              <div class="w-8 h-5 bg-white rounded flex items-center justify-center">
                <span class="text-xs font-bold text-blue-600">VISA</span>
              </div>
              <div class="w-8 h-5 bg-white rounded flex items-center justify-center">
                <span class="text-xs font-bold text-red-600">MC</span>
              </div>
              <div class="w-8 h-5 bg-white rounded flex items-center justify-center">
                <span class="text-xs font-bold text-blue-800">AMEX</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Back to Top Button -->
    <button
      v-if="showBackToTop"
      @click="scrollToTop"
      class="fixed bottom-6 right-6 w-12 h-12 bg-primary-900 text-white rounded-full shadow-lg hover:bg-primary-800 transition-all duration-300 transform hover:scale-110 z-40"
      title="Back to top"
    >
      <ChevronUpIcon class="w-6 h-6 mx-auto" />
    </button>
  </footer>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useProductsStore } from '@/stores/products'
import {
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChevronUpIcon
} from '@heroicons/vue/24/outline'

const productsStore = useProductsStore()

// Reactive data
const email = ref('')
const subscribing = ref(false)
const subscriptionMessage = ref('')
const subscriptionSuccess = ref(false)
const showBackToTop = ref(false)

// Computed properties
const currentYear = computed(() => new Date().getFullYear())
const categories = computed(() => productsStore.categories)

// Static data
const socialLinks = [
  {
    name: 'Facebook',
    url: 'https://facebook.com',
    icon: 'div' // In a real app, you'd use proper social media icons
  },
  {
    name: 'Instagram',
    url: 'https://instagram.com',
    icon: 'div'
  },
  {
    name: 'Twitter',
    url: 'https://twitter.com',
    icon: 'div'
  },
  {
    name: 'LinkedIn',
    url: 'https://linkedin.com',
    icon: 'div'
  }
]

const quickLinks = [
  { name: 'Home', path: '/' },
  { name: 'All Products', path: '/products' },
  { name: 'New Arrivals', path: '/products?filter=new' },
  { name: 'Featured', path: '/products?filter=featured' },
  { name: 'Sale', path: '/products?filter=sale' },
  { name: 'About Us', path: '/about' },
  { name: 'Contact', path: '/contact' }
]

// Methods
const subscribeNewsletter = async () => {
  subscribing.value = true
  subscriptionMessage.value = ''
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    subscriptionMessage.value = 'Thank you for subscribing to our newsletter!'
    subscriptionSuccess.value = true
    email.value = ''
  } catch (error) {
    subscriptionMessage.value = 'Subscription failed. Please try again.'
    subscriptionSuccess.value = false
  } finally {
    subscribing.value = false
    
    // Clear message after 5 seconds
    setTimeout(() => {
      subscriptionMessage.value = ''
    }, 5000)
  }
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* Smooth transitions for back to top button */
.fixed {
  transition: all 0.3s ease;
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #64748b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #475569;
}
</style>
