<template>
  <div class="cart-page">
    <!-- Header -->
    <section class="bg-primary-900 text-white py-12">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h1 class="text-4xl font-display font-bold mb-2">Shopping Cart</h1>
          <p class="text-primary-200">Review your items and proceed to checkout</p>
        </div>
      </div>
    </section>

    <div class="container mx-auto px-4 py-12">
      <!-- Empty Cart -->
      <div v-if="isEmpty" class="text-center py-20">
        <ShoppingCartIcon class="w-24 h-24 text-gray-300 mx-auto mb-6" />
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
        <p class="text-gray-600 mb-8">Looks like you haven't added any items to your cart yet.</p>
        <router-link to="/products" class="btn-primary">
          Continue Shopping
        </router-link>
      </div>

      <!-- Cart Items -->
      <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <!-- Items List -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-2xl font-bold text-primary-900 mb-6">
              Cart Items ({{ totalItems }})
            </h2>

            <div class="space-y-6">
              <CartItem
                v-for="item in items"
                :key="`${item.id}-${item.selectedSize}-${item.selectedColor}`"
                :item="item"
                @update-quantity="updateQuantity"
                @remove-item="removeItem"
                class="animate-fade-in-up"
              />
            </div>

            <!-- Continue Shopping -->
            <div class="mt-8 pt-6 border-t">
              <router-link to="/products" class="btn-secondary">
                <ArrowLeftIcon class="w-5 h-5 mr-2" />
                Continue Shopping
              </router-link>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-xl shadow-lg p-6 sticky top-6">
            <h3 class="text-xl font-bold text-primary-900 mb-6">Order Summary</h3>

            <div class="space-y-4">
              <!-- Subtotal -->
              <div class="flex justify-between">
                <span class="text-gray-600">Subtotal</span>
                <span class="font-semibold">{{ formattedTotalPrice }}</span>
              </div>

              <!-- Shipping -->
              <div class="flex justify-between">
                <span class="text-gray-600">Shipping</span>
                <span class="font-semibold text-green-600">Free</span>
              </div>

              <!-- Tax -->
              <div class="flex justify-between">
                <span class="text-gray-600">Tax</span>
                <span class="font-semibold">${{ tax.toFixed(2) }}</span>
              </div>

              <hr class="my-4">

              <!-- Total -->
              <div class="flex justify-between text-lg">
                <span class="font-bold text-primary-900">Total</span>
                <span class="font-bold text-primary-900">${{ finalTotal.toFixed(2) }}</span>
              </div>
            </div>

            <!-- Promo Code -->
            <div class="mt-6">
              <div class="flex gap-2">
                <input
                  v-model="promoCode"
                  type="text"
                  placeholder="Promo code"
                  class="input-field flex-1"
                >
                <button
                  @click="applyPromoCode"
                  :disabled="!promoCode || applyingPromo"
                  class="btn-secondary whitespace-nowrap"
                >
                  Apply
                </button>
              </div>
              <p v-if="promoMessage" :class="[
                'text-sm mt-2',
                promoSuccess ? 'text-green-600' : 'text-red-600'
              ]">
                {{ promoMessage }}
              </p>
            </div>

            <!-- Checkout Button -->
            <button
              @click="proceedToCheckout"
              :disabled="checkingOut"
              class="btn-primary w-full mt-6 text-lg py-4"
            >
              <span v-if="checkingOut">Processing...</span>
              <span v-else>Proceed to Checkout</span>
            </button>

            <!-- Security Badge -->
            <div class="mt-4 flex items-center justify-center text-sm text-gray-500">
              <ShieldCheckIcon class="w-4 h-4 mr-1" />
              Secure checkout
            </div>
          </div>

          <!-- Recently Viewed -->
          <div v-if="recentlyViewed.length > 0" class="mt-8 bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-bold text-primary-900 mb-4">Recently Viewed</h3>
            <div class="space-y-4">
              <div
                v-for="product in recentlyViewed.slice(0, 3)"
                :key="product.id"
                class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                @click="$router.push(`/product/${product.id}`)"
              >
                <img
                  :src="product.image"
                  :alt="product.name"
                  class="w-12 h-12 object-cover rounded"
                >
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">{{ product.name }}</p>
                  <p class="text-sm text-gray-500">${{ product.price.toFixed(2) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import CartItem from '@/components/ui/CartItem.vue'
import {
  ShoppingCartIcon,
  ArrowLeftIcon,
  ShieldCheckIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const cartStore = useCartStore()

// Reactive data
const promoCode = ref('')
const promoMessage = ref('')
const promoSuccess = ref(false)
const applyingPromo = ref(false)
const checkingOut = ref(false)
const discount = ref(0)

// Mock recently viewed products (in a real app, this would come from a store)
const recentlyViewed = ref([
  {
    id: 1,
    name: "Premium Cotton T-Shirt",
    price: 89.99,
    image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=100&h=100&fit=crop"
  },
  {
    id: 2,
    name: "Slim Fit Chinos",
    price: 149.99,
    image: "https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=100&h=100&fit=crop"
  }
])

// Computed properties
const items = computed(() => cartStore.items)
const totalItems = computed(() => cartStore.totalItems)
const isEmpty = computed(() => cartStore.isEmpty)
const formattedTotalPrice = computed(() => cartStore.formattedTotalPrice)
const subtotal = computed(() => cartStore.totalPrice)
const tax = computed(() => subtotal.value * 0.08) // 8% tax
const finalTotal = computed(() => subtotal.value + tax.value - discount.value)

// Methods
const updateQuantity = (itemId, selectedSize, selectedColor, quantity) => {
  cartStore.updateQuantity(itemId, selectedSize, selectedColor, quantity)
}

const removeItem = (itemId, selectedSize, selectedColor) => {
  cartStore.removeItem(itemId, selectedSize, selectedColor)
}

const applyPromoCode = async () => {
  applyingPromo.value = true
  promoMessage.value = ''
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock promo code validation
    const validCodes = {
      'SAVE10': 10,
      'WELCOME20': 20,
      'STUDENT15': 15
    }
    
    if (validCodes[promoCode.value.toUpperCase()]) {
      const discountAmount = validCodes[promoCode.value.toUpperCase()]
      discount.value = subtotal.value * (discountAmount / 100)
      promoMessage.value = `Promo code applied! You saved $${discount.value.toFixed(2)}`
      promoSuccess.value = true
      promoCode.value = ''
    } else {
      promoMessage.value = 'Invalid promo code'
      promoSuccess.value = false
    }
  } catch (error) {
    promoMessage.value = 'Failed to apply promo code'
    promoSuccess.value = false
  } finally {
    applyingPromo.value = false
    
    // Clear message after 5 seconds
    setTimeout(() => {
      promoMessage.value = ''
    }, 5000)
  }
}

const proceedToCheckout = async () => {
  checkingOut.value = true
  
  try {
    const result = await cartStore.checkout()
    
    if (result.success) {
      // Redirect to success page or show success message
      alert('Order placed successfully! Thank you for your purchase.')
      router.push('/')
    } else {
      alert(result.message)
    }
  } catch (error) {
    alert('Checkout failed. Please try again.')
  } finally {
    checkingOut.value = false
  }
}
</script>

<style scoped>
.cart-page {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
