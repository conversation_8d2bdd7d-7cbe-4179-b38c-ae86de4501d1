export const products = [
  {
    id: 1,
    name: "Premium Cotton T-Shirt",
    price: 89.99,
    originalPrice: 119.99,
    category: "t-shirts",
    image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=500&h=600&fit=crop"
    ],
    description: "Crafted from premium organic cotton, this t-shirt offers unparalleled comfort and style. Perfect for casual wear or layering.",
    features: ["100% Organic Cotton", "Pre-shrunk", "Reinforced seams", "Tagless design"],
    sizes: ["S", "M", "L", "XL", "XXL"],
    colors: ["Black", "White", "Navy", "Gray"],
    rating: 4.8,
    reviews: 124,
    inStock: true,
    isNew: false,
    isFeatured: true
  },
  {
    id: 2,
    name: "Slim Fit Chinos",
    price: 149.99,
    originalPrice: 199.99,
    category: "pants",
    image: "https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=500&h=600&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1506629905607-d9c297d3d45f?w=500&h=600&fit=crop"
    ],
    description: "Modern slim-fit chinos that bridge the gap between casual and formal. Versatile enough for office or weekend wear.",
    features: ["Stretch cotton blend", "Wrinkle-resistant", "Tapered fit", "Side adjusters"],
    sizes: ["28", "30", "32", "34", "36", "38"],
    colors: ["Khaki", "Navy", "Black", "Olive"],
    rating: 4.6,
    reviews: 89,
    inStock: true,
    isNew: true,
    isFeatured: true
  },
  {
    id: 3,
    name: "Classic Denim Jacket",
    price: 199.99,
    originalPrice: 249.99,
    category: "jackets",
    image: "https://images.unsplash.com/photo-1551028719-00167b16eac5?w=500&h=600&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1551028719-00167b16eac5?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=600&fit=crop"
    ],
    description: "Timeless denim jacket with a modern twist. Features premium denim construction and classic styling that never goes out of fashion.",
    features: ["100% Cotton Denim", "Stone washed", "Button closure", "Chest pockets"],
    sizes: ["S", "M", "L", "XL", "XXL"],
    colors: ["Light Blue", "Dark Blue", "Black"],
    rating: 4.9,
    reviews: 156,
    inStock: true,
    isNew: false,
    isFeatured: true
  },
  {
    id: 4,
    name: "Merino Wool Sweater",
    price: 179.99,
    originalPrice: 229.99,
    category: "sweaters",
    image: "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=500&h=600&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1618354691373-d851c5c3a990?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1571945153237-4929e783af4a?w=500&h=600&fit=crop"
    ],
    description: "Luxurious merino wool sweater that provides warmth without bulk. Perfect for layering or wearing on its own.",
    features: ["100% Merino Wool", "Machine washable", "Crew neck", "Ribbed cuffs"],
    sizes: ["S", "M", "L", "XL"],
    colors: ["Charcoal", "Navy", "Cream", "Burgundy"],
    rating: 4.7,
    reviews: 73,
    inStock: true,
    isNew: true,
    isFeatured: false
  },
  {
    id: 5,
    name: "Oxford Button-Down Shirt",
    price: 129.99,
    originalPrice: 159.99,
    category: "shirts",
    image: "https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=500&h=600&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1564859228273-274232fdb516?w=500&h=600&fit=crop"
    ],
    description: "Classic Oxford button-down shirt crafted from premium cotton. A wardrobe essential that works for both business and casual occasions.",
    features: ["100% Cotton Oxford", "Button-down collar", "Chest pocket", "Curved hem"],
    sizes: ["S", "M", "L", "XL", "XXL"],
    colors: ["White", "Light Blue", "Pink", "Yellow"],
    rating: 4.5,
    reviews: 92,
    inStock: true,
    isNew: false,
    isFeatured: false
  },
  {
    id: 6,
    name: "Leather Sneakers",
    price: 249.99,
    originalPrice: 299.99,
    category: "shoes",
    image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500&h=600&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=500&h=600&fit=crop",
      "https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=500&h=600&fit=crop"
    ],
    description: "Premium leather sneakers that combine comfort with sophisticated style. Perfect for casual and smart-casual occasions.",
    features: ["Genuine leather upper", "Cushioned insole", "Rubber outsole", "Lace-up closure"],
    sizes: ["7", "8", "9", "10", "11", "12"],
    colors: ["White", "Black", "Brown"],
    rating: 4.8,
    reviews: 167,
    inStock: true,
    isNew: true,
    isFeatured: true
  }
];

export const categories = [
  { id: 'all', name: 'All Products', count: products.length },
  { id: 't-shirts', name: 'T-Shirts', count: products.filter(p => p.category === 't-shirts').length },
  { id: 'shirts', name: 'Shirts', count: products.filter(p => p.category === 'shirts').length },
  { id: 'pants', name: 'Pants', count: products.filter(p => p.category === 'pants').length },
  { id: 'jackets', name: 'Jackets', count: products.filter(p => p.category === 'jackets').length },
  { id: 'sweaters', name: 'Sweaters', count: products.filter(p => p.category === 'sweaters').length },
  { id: 'shoes', name: 'Shoes', count: products.filter(p => p.category === 'shoes').length }
];

export const featuredProducts = products.filter(product => product.isFeatured);
export const newProducts = products.filter(product => product.isNew);
