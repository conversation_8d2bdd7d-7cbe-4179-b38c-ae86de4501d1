<template>
  <div 
    :class="[
      'product-card group',
      theme === 'dark' ? 'bg-primary-800 text-white' : 'bg-white'
    ]"
    @click="navigateToProduct"
  >
    <!-- Product Image -->
    <div class="relative aspect-square overflow-hidden rounded-t-xl">
      <img
        :src="product.image"
        :alt="product.name"
        class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        loading="lazy"
      />
      
      <!-- Badges -->
      <div class="absolute top-4 left-4 flex flex-col gap-2">
        <span v-if="product.isNew" class="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
          NEW
        </span>
        <span v-if="product.isFeatured" class="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full">
          FEATURED
        </span>
        <span v-if="hasDiscount" class="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
          -{{ discountPercentage }}%
        </span>
      </div>

      <!-- Quick Actions -->
      <div class="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <button
          @click.stop="toggleWishlist"
          :class="[
            'p-2 rounded-full transition-all duration-200 backdrop-blur-sm',
            isInWishlist ? 'bg-red-500 text-white' : 'bg-white/80 text-gray-700 hover:bg-white'
          ]"
          :title="isInWishlist ? 'Remove from wishlist' : 'Add to wishlist'"
        >
          <HeartIcon :class="['w-5 h-5', isInWishlist ? 'fill-current' : '']" />
        </button>
        
        <button
          @click.stop="quickView"
          class="p-2 rounded-full bg-white/80 text-gray-700 hover:bg-white transition-all duration-200 backdrop-blur-sm"
          title="Quick view"
        >
          <EyeIcon class="w-5 h-5" />
        </button>
      </div>

      <!-- Quick Add to Cart -->
      <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
        <button
          @click.stop="quickAddToCart"
          :disabled="!product.inStock || addingToCart"
          :class="[
            'w-full py-2 px-4 rounded-lg font-medium transition-all duration-200 backdrop-blur-sm',
            product.inStock
              ? 'bg-primary-900/90 text-white hover:bg-primary-900'
              : 'bg-gray-400/90 text-white cursor-not-allowed'
          ]"
        >
          <span v-if="addingToCart" class="flex items-center justify-center gap-2">
            <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Adding...
          </span>
          <span v-else-if="!product.inStock">Out of Stock</span>
          <span v-else class="flex items-center justify-center gap-2">
            <ShoppingCartIcon class="w-4 h-4" />
            Quick Add
          </span>
        </button>
      </div>
    </div>

    <!-- Product Info -->
    <div class="p-6">
      <!-- Product Name -->
      <h3 :class="[
        'font-semibold text-lg mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors',
        theme === 'dark' ? 'text-white group-hover:text-primary-300' : 'text-gray-900'
      ]">
        {{ product.name }}
      </h3>

      <!-- Product Category -->
      <p :class="[
        'text-sm mb-3 capitalize',
        theme === 'dark' ? 'text-primary-200' : 'text-gray-600'
      ]">
        {{ product.category.replace('-', ' ') }}
      </p>

      <!-- Rating -->
      <div class="flex items-center gap-2 mb-3">
        <div class="flex items-center">
          <StarIcon
            v-for="i in 5"
            :key="i"
            :class="[
              'w-4 h-4',
              i <= Math.floor(product.rating) 
                ? 'text-yellow-400 fill-current' 
                : theme === 'dark' ? 'text-gray-600' : 'text-gray-300'
            ]"
          />
        </div>
        <span :class="[
          'text-sm',
          theme === 'dark' ? 'text-primary-200' : 'text-gray-600'
        ]">
          {{ product.rating }} ({{ product.reviews }})
        </span>
      </div>

      <!-- Price -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <span :class="[
            'text-xl font-bold',
            theme === 'dark' ? 'text-white' : 'text-primary-900'
          ]">
            ${{ product.price.toFixed(2) }}
          </span>
          <span v-if="hasDiscount" :class="[
            'text-sm line-through',
            theme === 'dark' ? 'text-primary-300' : 'text-gray-500'
          ]">
            ${{ product.originalPrice.toFixed(2) }}
          </span>
        </div>

        <!-- Stock Status -->
        <div class="flex items-center gap-1">
          <div :class="[
            'w-2 h-2 rounded-full',
            product.inStock ? 'bg-green-500' : 'bg-red-500'
          ]"></div>
          <span :class="[
            'text-xs font-medium',
            product.inStock 
              ? theme === 'dark' ? 'text-green-300' : 'text-green-600'
              : theme === 'dark' ? 'text-red-300' : 'text-red-600'
          ]">
            {{ product.inStock ? 'In Stock' : 'Out of Stock' }}
          </span>
        </div>
      </div>

      <!-- Features Preview -->
      <div v-if="product.features && product.features.length > 0" class="mt-3">
        <div class="flex flex-wrap gap-1">
          <span
            v-for="feature in product.features.slice(0, 2)"
            :key="feature"
            :class="[
              'text-xs px-2 py-1 rounded-full',
              theme === 'dark' 
                ? 'bg-primary-700 text-primary-200' 
                : 'bg-gray-100 text-gray-600'
            ]"
          >
            {{ feature }}
          </span>
          <span
            v-if="product.features.length > 2"
            :class="[
              'text-xs px-2 py-1 rounded-full',
              theme === 'dark' 
                ? 'bg-primary-700 text-primary-200' 
                : 'bg-gray-100 text-gray-600'
            ]"
          >
            +{{ product.features.length - 2 }} more
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import {
  HeartIcon,
  EyeIcon,
  ShoppingCartIcon,
  StarIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const cartStore = useCartStore()

// Props
const props = defineProps({
  product: {
    type: Object,
    required: true
  },
  theme: {
    type: String,
    default: 'light',
    validator: (value) => ['light', 'dark'].includes(value)
  }
})

// Reactive data
const addingToCart = ref(false)
const isInWishlist = ref(false) // In a real app, this would come from a wishlist store

// Computed properties
const hasDiscount = computed(() => {
  return props.product.originalPrice > props.product.price
})

const discountPercentage = computed(() => {
  if (!hasDiscount.value) return 0
  return Math.round(((props.product.originalPrice - props.product.price) / props.product.originalPrice) * 100)
})

// Methods
const navigateToProduct = () => {
  router.push(`/product/${props.product.id}`)
}

const toggleWishlist = () => {
  isInWishlist.value = !isInWishlist.value
  // In a real app, you would save this to a wishlist store or API
}

const quickView = () => {
  // In a real app, this would open a modal with product details
  navigateToProduct()
}

const quickAddToCart = async () => {
  if (!props.product.inStock || addingToCart.value) return
  
  addingToCart.value = true
  
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // Add to cart with default selections
    cartStore.addItem(props.product, null, null, 1)
    
    // Show success feedback
    // In a real app, you might show a toast notification
    
  } catch (error) {
    console.error('Failed to add to cart:', error)
  } finally {
    addingToCart.value = false
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
