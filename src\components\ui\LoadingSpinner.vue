<template>
  <div class="loading-spinner-container" :class="containerClass">
    <div 
      :class="[
        'loading-spinner',
        sizeClass,
        colorClass
      ]"
    ></div>
    <p v-if="text" :class="textClass">{{ text }}</p>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  color: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'white', 'gray'].includes(value)
  },
  text: {
    type: String,
    default: ''
  },
  center: {
    type: Boolean,
    default: true
  }
})

// Computed properties
const sizeClass = computed(() => {
  const sizes = {
    xs: 'w-4 h-4 border-2',
    sm: 'w-6 h-6 border-2',
    md: 'w-8 h-8 border-4',
    lg: 'w-12 h-12 border-4',
    xl: 'w-16 h-16 border-4'
  }
  return sizes[props.size]
})

const colorClass = computed(() => {
  const colors = {
    primary: 'border-primary-200 border-t-primary-900',
    white: 'border-white/30 border-t-white',
    gray: 'border-gray-200 border-t-gray-600'
  }
  return colors[props.color]
})

const containerClass = computed(() => {
  return props.center ? 'flex flex-col items-center justify-center' : ''
})

const textClass = computed(() => {
  const textColors = {
    primary: 'text-primary-700',
    white: 'text-white',
    gray: 'text-gray-600'
  }
  
  const textSizes = {
    xs: 'text-xs mt-1',
    sm: 'text-sm mt-2',
    md: 'text-sm mt-2',
    lg: 'text-base mt-3',
    xl: 'text-lg mt-4'
  }
  
  return `${textColors[props.color]} ${textSizes[props.size]} font-medium`
})
</script>

<style scoped>
.loading-spinner {
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Smooth animation */
.loading-spinner-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
