<template>
  <header class="bg-white shadow-lg sticky top-0 z-50">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-16 lg:h-20">
        <!-- Logo -->
        <router-link to="/" class="flex items-center space-x-2 group">
          <div class="w-10 h-10 bg-primary-900 rounded-lg flex items-center justify-center group-hover:bg-primary-800 transition-colors">
            <span class="text-white font-bold text-xl">K</span>
          </div>
          <div class="hidden sm:block">
            <h1 class="text-xl lg:text-2xl font-display font-bold text-primary-900">
              Kayışdağı Store
            </h1>
            <p class="text-xs text-gray-600 -mt-1">Premium Men's Fashion</p>
          </div>
        </router-link>

        <!-- Desktop Navigation -->
        <nav class="hidden lg:flex items-center space-x-8">
          <router-link
            to="/"
            class="nav-link"
            :class="{ 'text-primary-900': $route.path === '/' }"
          >
            Home
          </router-link>
          <router-link
            to="/products"
            class="nav-link"
            :class="{ 'text-primary-900': $route.path.startsWith('/products') }"
          >
            Products
          </router-link>
          
          <!-- Categories Dropdown -->
          <div class="relative group">
            <button class="nav-link flex items-center gap-1">
              Categories
              <ChevronDownIcon class="w-4 h-4 transition-transform group-hover:rotate-180" />
            </button>
            
            <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 transform translate-y-2 group-hover:translate-y-0">
              <div class="py-2">
                <router-link
                  v-for="category in categories.filter(c => c.id !== 'all')"
                  :key="category.id"
                  :to="`/products/${category.id}`"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-900 transition-colors"
                >
                  {{ category.name }}
                  <span class="text-gray-400 text-xs ml-1">({{ category.count }})</span>
                </router-link>
              </div>
            </div>
          </div>
        </nav>

        <!-- Right Side Actions -->
        <div class="flex items-center space-x-4">
          <!-- Search (Desktop) -->
          <div class="hidden md:block w-64">
            <SearchBar @search="handleSearch" />
          </div>

          <!-- Cart Button -->
          <button
            @click="toggleCart"
            class="relative p-2 text-gray-700 hover:text-primary-900 transition-colors cart-icon"
          >
            <ShoppingCartIcon class="w-6 h-6" />
            <span
              v-if="cartItemCount > 0"
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center animate-bounce-in"
            >
              {{ cartItemCount > 99 ? '99+' : cartItemCount }}
            </span>
          </button>

          <!-- Mobile Menu Button -->
          <button
            @click="toggleMobileMenu"
            class="lg:hidden p-2 text-gray-700 hover:text-primary-900 transition-colors"
          >
            <Bars3Icon v-if="!mobileMenuOpen" class="w-6 h-6" />
            <XMarkIcon v-else class="w-6 h-6" />
          </button>
        </div>
      </div>

      <!-- Mobile Search -->
      <div class="md:hidden pb-4">
        <SearchBar @search="handleSearch" />
      </div>
    </div>

    <!-- Mobile Menu -->
    <Transition name="mobile-menu">
      <div v-if="mobileMenuOpen" class="lg:hidden bg-white border-t">
        <div class="container mx-auto px-4 py-4">
          <nav class="space-y-4">
            <router-link
              to="/"
              @click="closeMobileMenu"
              class="block text-lg font-medium text-gray-700 hover:text-primary-900 transition-colors"
              :class="{ 'text-primary-900': $route.path === '/' }"
            >
              Home
            </router-link>
            <router-link
              to="/products"
              @click="closeMobileMenu"
              class="block text-lg font-medium text-gray-700 hover:text-primary-900 transition-colors"
              :class="{ 'text-primary-900': $route.path.startsWith('/products') }"
            >
              All Products
            </router-link>
            
            <!-- Mobile Categories -->
            <div class="space-y-2">
              <p class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Categories</p>
              <router-link
                v-for="category in categories.filter(c => c.id !== 'all')"
                :key="category.id"
                :to="`/products/${category.id}`"
                @click="closeMobileMenu"
                class="block pl-4 text-gray-700 hover:text-primary-900 transition-colors"
              >
                {{ category.name }}
                <span class="text-gray-400 text-sm ml-1">({{ category.count }})</span>
              </router-link>
            </div>
          </nav>
        </div>
      </div>
    </Transition>

    <!-- Cart Sidebar -->
    <Transition name="cart-sidebar">
      <div v-if="cartOpen" class="fixed inset-0 z-50 overflow-hidden">
        <!-- Backdrop -->
        <div
          class="absolute inset-0 bg-black bg-opacity-50"
          @click="closeCart"
        ></div>
        
        <!-- Cart Panel -->
        <div class="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
          <div class="flex flex-col h-full">
            <!-- Cart Header -->
            <div class="flex items-center justify-between p-6 border-b">
              <h2 class="text-lg font-semibold text-primary-900">
                Shopping Cart ({{ cartItemCount }})
              </h2>
              <button
                @click="closeCart"
                class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <XMarkIcon class="w-5 h-5" />
              </button>
            </div>

            <!-- Cart Content -->
            <div class="flex-1 overflow-y-auto p-6">
              <div v-if="cartItems.length === 0" class="text-center py-12">
                <ShoppingCartIcon class="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <p class="text-gray-600 mb-4">Your cart is empty</p>
                <button @click="closeCart" class="btn-primary">
                  Continue Shopping
                </button>
              </div>

              <div v-else class="space-y-4">
                <div
                  v-for="item in cartItems"
                  :key="`${item.id}-${item.selectedSize}-${item.selectedColor}`"
                  class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg"
                >
                  <img
                    :src="item.image"
                    :alt="item.name"
                    class="w-16 h-16 object-cover rounded"
                  />
                  <div class="flex-1 min-w-0">
                    <h4 class="font-medium text-gray-900 truncate">{{ item.name }}</h4>
                    <p class="text-sm text-gray-600">
                      {{ item.selectedSize ? `Size: ${item.selectedSize}` : '' }}
                      {{ item.selectedColor ? ` • Color: ${item.selectedColor}` : '' }}
                    </p>
                    <div class="flex items-center justify-between mt-1">
                      <span class="font-semibold text-primary-900">
                        ${{ (item.price * item.quantity).toFixed(2) }}
                      </span>
                      <div class="flex items-center gap-2">
                        <button
                          @click="decreaseQuantity(item)"
                          class="w-6 h-6 flex items-center justify-center border border-gray-300 rounded text-sm hover:bg-gray-50"
                        >
                          -
                        </button>
                        <span class="text-sm">{{ item.quantity }}</span>
                        <button
                          @click="increaseQuantity(item)"
                          class="w-6 h-6 flex items-center justify-center border border-gray-300 rounded text-sm hover:bg-gray-50"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Cart Footer -->
            <div v-if="cartItems.length > 0" class="border-t p-6">
              <div class="flex justify-between items-center mb-4">
                <span class="text-lg font-semibold text-primary-900">Total:</span>
                <span class="text-xl font-bold text-primary-900">{{ formattedTotalPrice }}</span>
              </div>
              <div class="space-y-3">
                <router-link
                  to="/cart"
                  @click="closeCart"
                  class="btn-secondary w-full text-center"
                >
                  View Cart
                </router-link>
                <button class="btn-primary w-full">
                  Checkout
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useCartStore } from '@/stores/cart'
import SearchBar from '@/components/ui/SearchBar.vue'
import {
  ShoppingCartIcon,
  Bars3Icon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const productsStore = useProductsStore()
const cartStore = useCartStore()

// Reactive data
const mobileMenuOpen = ref(false)
const cartOpen = ref(false)

// Computed properties
const categories = computed(() => productsStore.categories)
const cartItemCount = computed(() => cartStore.totalItems)
const cartItems = computed(() => cartStore.items)
const formattedTotalPrice = computed(() => cartStore.formattedTotalPrice)

// Methods
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const toggleCart = () => {
  cartOpen.value = !cartOpen.value
}

const closeCart = () => {
  cartOpen.value = false
}

const handleSearch = (query) => {
  closeMobileMenu()
  // Search handling is done in SearchBar component
}

const increaseQuantity = (item) => {
  cartStore.increaseQuantity(item.id, item.selectedSize, item.selectedColor)
}

const decreaseQuantity = (item) => {
  cartStore.decreaseQuantity(item.id, item.selectedSize, item.selectedColor)
}

// Close mobile menu on route change
const handleRouteChange = () => {
  closeMobileMenu()
}

// Close menus on escape key
const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    closeMobileMenu()
    closeCart()
  }
}

// Lifecycle
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  router.afterEach(handleRouteChange)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* Mobile menu transitions */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Cart sidebar transitions */
.cart-sidebar-enter-active,
.cart-sidebar-leave-active {
  transition: all 0.3s ease;
}

.cart-sidebar-enter-from,
.cart-sidebar-leave-to {
  opacity: 0;
}

.cart-sidebar-enter-from .absolute.right-0,
.cart-sidebar-leave-to .absolute.right-0 {
  transform: translateX(100%);
}
</style>
