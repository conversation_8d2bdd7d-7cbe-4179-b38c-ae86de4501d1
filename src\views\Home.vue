<template>
  <div class="home">
    <!-- Hero Section -->
    <section class="hero-gradient min-h-screen flex items-center justify-center relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center text-white">
          <h1 class="text-5xl md:text-7xl font-display font-bold mb-6 animate-fade-in-up">
            Kayışdağı Store
          </h1>
          <p class="text-xl md:text-2xl mb-8 max-w-2xl mx-auto animate-fade-in-up" style="animation-delay: 0.2s;">
            Premium men's clothing for the modern gentleman. Discover quality, style, and sophistication.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up" style="animation-delay: 0.4s;">
            <router-link to="/products" class="btn-primary text-lg px-8 py-4">
              Shop Collection
            </router-link>
            <button @click="scrollToFeatured" class="btn-secondary text-lg px-8 py-4">
              View Featured
            </button>
          </div>
        </div>
      </div>
      
      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <ChevronDownIcon class="w-8 h-8 text-white" />
      </div>
    </section>

    <!-- Featured Products Section -->
    <section id="featured" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-display font-bold text-primary-900 mb-4 scroll-animate">
            Featured Collection
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto scroll-animate">
            Handpicked pieces that define modern masculine style
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProductCard 
            v-for="(product, index) in featuredProducts" 
            :key="product.id"
            :product="product"
            :class="`scroll-animate`"
            :style="`animation-delay: ${index * 0.1}s`"
          />
        </div>

        <div class="text-center mt-12">
          <router-link to="/products" class="btn-primary">
            View All Products
          </router-link>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="py-20">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-display font-bold text-primary-900 mb-4 scroll-animate">
            Shop by Category
          </h2>
          <p class="text-xl text-gray-600 scroll-animate">
            Find exactly what you're looking for
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          <div 
            v-for="(category, index) in categories.filter(c => c.id !== 'all')" 
            :key="category.id"
            class="category-card group cursor-pointer scroll-animate"
            :style="`animation-delay: ${index * 0.1}s`"
            @click="navigateToCategory(category.id)"
          >
            <div class="bg-white rounded-xl p-8 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors">
                <component :is="getCategoryIcon(category.id)" class="w-8 h-8 text-primary-700" />
              </div>
              <h3 class="text-lg font-semibold text-primary-900 mb-2">{{ category.name }}</h3>
              <p class="text-gray-600">{{ category.count }} items</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- New Arrivals Section -->
    <section class="py-20 bg-primary-900 text-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-display font-bold mb-4 scroll-animate">
            New Arrivals
          </h2>
          <p class="text-xl text-primary-200 scroll-animate">
            Fresh styles for the season
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProductCard 
            v-for="(product, index) in newProducts" 
            :key="product.id"
            :product="product"
            :class="`scroll-animate`"
            :style="`animation-delay: ${index * 0.1}s`"
            theme="dark"
          />
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-20 bg-gray-100">
      <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto text-center">
          <h2 class="text-3xl md:text-4xl font-display font-bold text-primary-900 mb-4 scroll-animate">
            Stay Updated
          </h2>
          <p class="text-lg text-gray-600 mb-8 scroll-animate">
            Subscribe to our newsletter for exclusive offers and style updates
          </p>
          
          <form @submit.prevent="subscribeNewsletter" class="flex flex-col sm:flex-row gap-4 scroll-animate">
            <input 
              v-model="email"
              type="email" 
              placeholder="Enter your email"
              class="input-field flex-1"
              required
            >
            <button type="submit" class="btn-primary whitespace-nowrap" :disabled="subscribing">
              <span v-if="subscribing">Subscribing...</span>
              <span v-else>Subscribe</span>
            </button>
          </form>
          
          <p v-if="subscriptionMessage" class="mt-4 text-sm" :class="subscriptionSuccess ? 'text-green-600' : 'text-red-600'">
            {{ subscriptionMessage }}
          </p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import ProductCard from '@/components/ui/ProductCard.vue'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'
import { 
  ShirtIcon, 
  CubeIcon, 
  SparklesIcon,
  FireIcon,
  StarIcon,
  BoltIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const productsStore = useProductsStore()

// Reactive data
const email = ref('')
const subscribing = ref(false)
const subscriptionMessage = ref('')
const subscriptionSuccess = ref(false)

// Computed properties
const featuredProducts = productsStore.featuredProducts
const newProducts = productsStore.newProducts
const categories = productsStore.categories

// Methods
const scrollToFeatured = () => {
  document.getElementById('featured').scrollIntoView({ behavior: 'smooth' })
}

const navigateToCategory = (categoryId) => {
  router.push(`/products/${categoryId}`)
}

const getCategoryIcon = (categoryId) => {
  const icons = {
    't-shirts': ShirtIcon,
    'shirts': CubeIcon,
    'pants': SparklesIcon,
    'jackets': FireIcon,
    'sweaters': StarIcon,
    'shoes': BoltIcon
  }
  return icons[categoryId] || CubeIcon
}

const subscribeNewsletter = async () => {
  subscribing.value = true
  subscriptionMessage.value = ''

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))

    subscriptionMessage.value = 'Thank you for subscribing!'
    subscriptionSuccess.value = true
    email.value = ''
  } catch (error) {
    subscriptionMessage.value = 'Subscription failed. Please try again.'
    subscriptionSuccess.value = false
  } finally {
    subscribing.value = false

    // Clear message after 5 seconds
    setTimeout(() => {
      subscriptionMessage.value = ''
    }, 5000)
  }
}

// Scroll animations
const handleScroll = () => {
  const elements = document.querySelectorAll('.scroll-animate')
  elements.forEach(element => {
    const elementTop = element.getBoundingClientRect().top
    const elementVisible = 150
    
    if (elementTop < window.innerHeight - elementVisible) {
      element.classList.add('animate')
    }
  })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  handleScroll() // Check initial state
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.category-card {
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-8px);
}
</style>
