<template>
  <div class="search-bar relative">
    <div class="relative">
      <!-- Search Input -->
      <input
        v-model="searchQuery"
        @input="handleInput"
        @focus="showSuggestions = true"
        @blur="handleBlur"
        @keydown.enter="handleSearch"
        @keydown.escape="clearSearch"
        @keydown.arrow-down="navigateSuggestions(1)"
        @keydown.arrow-up="navigateSuggestions(-1)"
        type="text"
        placeholder="Search products..."
        class="w-full pl-12 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
        :class="{ 'border-primary-500': showSuggestions && suggestions.length > 0 }"
      />

      <!-- Search Icon -->
      <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
        <MagnifyingGlassIcon class="w-5 h-5 text-gray-400" />
      </div>

      <!-- Loading Spinner -->
      <div v-if="loading" class="absolute right-4 top-1/2 transform -translate-y-1/2">
        <div class="w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
      </div>

      <!-- Clear Button -->
      <button
        v-else-if="searchQuery"
        @click="clearSearch"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded-full transition-colors"
        title="Clear search"
      >
        <XMarkIcon class="w-4 h-4 text-gray-400" />
      </button>
    </div>

    <!-- Search Suggestions -->
    <div
      v-if="showSuggestions && suggestions.length > 0"
      class="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
    >
      <!-- Recent Searches -->
      <div v-if="recentSearches.length > 0 && !searchQuery" class="p-4 border-b border-gray-100">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Recent Searches</h4>
        <div class="space-y-1">
          <button
            v-for="(search, index) in recentSearches.slice(0, 3)"
            :key="`recent-${index}`"
            @click="selectSuggestion(search)"
            class="flex items-center gap-2 w-full text-left px-2 py-1 text-sm text-gray-600 hover:bg-gray-50 rounded"
          >
            <ClockIcon class="w-4 h-4 text-gray-400" />
            {{ search }}
          </button>
        </div>
      </div>

      <!-- Product Suggestions -->
      <div class="p-2">
        <div
          v-for="(suggestion, index) in suggestions"
          :key="suggestion.id"
          @mousedown="selectProductSuggestion(suggestion)"
          :class="[
            'flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors',
            index === selectedSuggestionIndex ? 'bg-primary-50' : 'hover:bg-gray-50'
          ]"
        >
          <!-- Product Image -->
          <img
            :src="suggestion.image"
            :alt="suggestion.name"
            class="w-12 h-12 object-cover rounded-lg"
          />

          <!-- Product Info -->
          <div class="flex-1 min-w-0">
            <p class="font-medium text-gray-900 truncate">
              {{ suggestion.name }}
            </p>
            <p class="text-sm text-gray-500 capitalize">
              {{ suggestion.category.replace('-', ' ') }}
            </p>
            <p class="text-sm font-semibold text-primary-600">
              ${{ suggestion.price.toFixed(2) }}
            </p>
          </div>

          <!-- Rating -->
          <div class="flex items-center gap-1">
            <StarIcon class="w-4 h-4 text-yellow-400 fill-current" />
            <span class="text-sm text-gray-600">{{ suggestion.rating }}</span>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-if="searchQuery && suggestions.length === 0 && !loading" class="p-4 text-center text-gray-500">
        <MagnifyingGlassIcon class="w-8 h-8 mx-auto mb-2 text-gray-300" />
        <p class="text-sm">No products found for "{{ searchQuery }}"</p>
        <p class="text-xs mt-1">Try different keywords or browse categories</p>
      </div>

      <!-- Popular Searches -->
      <div v-if="!searchQuery && popularSearches.length > 0" class="p-4 border-t border-gray-100">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Popular Searches</h4>
        <div class="flex flex-wrap gap-2">
          <button
            v-for="search in popularSearches"
            :key="search"
            @click="selectSuggestion(search)"
            class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
          >
            {{ search }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const productsStore = useProductsStore()

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['search'])

// Reactive data
const searchQuery = ref('')
const showSuggestions = ref(false)
const selectedSuggestionIndex = ref(-1)
const recentSearches = ref([])
const popularSearches = ref([
  'T-Shirts',
  'Jeans',
  'Sneakers',
  'Jackets',
  'Formal Shirts'
])

// Computed properties
const suggestions = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) {
    return []
  }

  return productsStore.products
    .filter(product =>
      product.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      product.category.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      product.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
    .slice(0, 5) // Limit to 5 suggestions
})

// Methods
const handleInput = () => {
  selectedSuggestionIndex.value = -1
  
  // Show suggestions after typing 2+ characters
  if (searchQuery.value.length >= 2) {
    showSuggestions.value = true
  } else {
    showSuggestions.value = false
  }
}

const handleBlur = () => {
  // Delay hiding suggestions to allow for clicks
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

const handleSearch = () => {
  if (selectedSuggestionIndex.value >= 0 && suggestions.value.length > 0) {
    // Navigate to selected product
    selectProductSuggestion(suggestions.value[selectedSuggestionIndex.value])
  } else if (searchQuery.value.trim()) {
    // Perform search
    performSearch(searchQuery.value.trim())
  }
}

const performSearch = (query) => {
  // Add to recent searches
  addToRecentSearches(query)
  
  // Hide suggestions
  showSuggestions.value = false
  
  // Emit search event
  emit('search', query)
  
  // Navigate to products page with search
  router.push({
    path: '/products',
    query: { search: query }
  })
}

const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion
  performSearch(suggestion)
}

const selectProductSuggestion = (product) => {
  // Add product name to recent searches
  addToRecentSearches(product.name)
  
  // Hide suggestions
  showSuggestions.value = false
  
  // Navigate to product detail
  router.push(`/product/${product.id}`)
}

const navigateSuggestions = (direction) => {
  if (!showSuggestions.value || suggestions.value.length === 0) return
  
  const maxIndex = suggestions.value.length - 1
  
  if (direction === 1) {
    // Arrow down
    selectedSuggestionIndex.value = selectedSuggestionIndex.value < maxIndex 
      ? selectedSuggestionIndex.value + 1 
      : 0
  } else {
    // Arrow up
    selectedSuggestionIndex.value = selectedSuggestionIndex.value > 0 
      ? selectedSuggestionIndex.value - 1 
      : maxIndex
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
  selectedSuggestionIndex.value = -1
  emit('search', '')
}

const addToRecentSearches = (query) => {
  // Remove if already exists
  const index = recentSearches.value.indexOf(query)
  if (index > -1) {
    recentSearches.value.splice(index, 1)
  }
  
  // Add to beginning
  recentSearches.value.unshift(query)
  
  // Keep only last 10 searches
  recentSearches.value = recentSearches.value.slice(0, 10)
  
  // Save to localStorage
  localStorage.setItem('kayisdagi-recent-searches', JSON.stringify(recentSearches.value))
}

const loadRecentSearches = () => {
  try {
    const saved = localStorage.getItem('kayisdagi-recent-searches')
    if (saved) {
      recentSearches.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('Failed to load recent searches:', error)
  }
}

// Watch for route changes to update search query
watch(() => router.currentRoute.value.query.search, (newSearch) => {
  if (newSearch && newSearch !== searchQuery.value) {
    searchQuery.value = newSearch
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  loadRecentSearches()
})
</script>

<style scoped>
.search-bar {
  position: relative;
}

/* Custom scrollbar for suggestions */
.search-bar ::-webkit-scrollbar {
  width: 6px;
}

.search-bar ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.search-bar ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.search-bar ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
