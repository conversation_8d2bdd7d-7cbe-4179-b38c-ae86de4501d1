import { createRouter, createWebHistory } from 'vue-router'

// Lazy load components for better performance
const Home = () => import('@/views/Home.vue')
const Products = () => import('@/views/Products.vue')
const ProductDetail = () => import('@/views/ProductDetail.vue')
const Cart = () => import('@/views/Cart.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'Kayışdağı Store - Premium Men\'s Clothing',
      description: 'Discover premium men\'s clothing at Kayışdağı Store. Quality fashion for the modern man.'
    }
  },
  {
    path: '/products',
    name: 'Products',
    component: Products,
    meta: {
      title: 'Products - Kayışdağı Store',
      description: 'Browse our collection of premium men\'s clothing and accessories.'
    }
  },
  {
    path: '/products/:category',
    name: 'ProductsByCategory',
    component: Products,
    props: true,
    meta: {
      title: 'Products - Kayışdağı Store',
      description: 'Browse our collection by category.'
    }
  },
  {
    path: '/product/:id',
    name: 'ProductDetail',
    component: ProductDetail,
    props: true,
    meta: {
      title: 'Product Details - Kayışdağı Store',
      description: 'View detailed information about our premium products.'
    }
  },
  {
    path: '/cart',
    name: 'Cart',
    component: Cart,
    meta: {
      title: 'Shopping Cart - Kayışdağı Store',
      description: 'Review your selected items and proceed to checkout.'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return {
        el: to.hash,
        behavior: 'smooth'
      }
    } else {
      return { top: 0, behavior: 'smooth' }
    }
  }
})

// Navigation guards for meta tags and page transitions
router.beforeEach((to, from, next) => {
  // Update document title
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // Update meta description
  const metaDescription = document.querySelector('meta[name="description"]')
  if (metaDescription && to.meta.description) {
    metaDescription.setAttribute('content', to.meta.description)
  }

  // Add page transition class
  const app = document.getElementById('app')
  if (app) {
    app.classList.add('page-transitioning')
  }

  next()
})

router.afterEach(() => {
  // Remove page transition class after navigation
  setTimeout(() => {
    const app = document.getElementById('app')
    if (app) {
      app.classList.remove('page-transitioning')
    }
  }, 400)
})

export default router
