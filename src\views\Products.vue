<template>
  <div class="products-page">
    <!-- Header Section -->
    <section class="bg-primary-900 text-white py-16">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-display font-bold mb-4 animate-fade-in-up">
            {{ pageTitle }}
          </h1>
          <p class="text-xl text-primary-200 animate-fade-in-up" style="animation-delay: 0.2s;">
            {{ pageDescription }}
          </p>
        </div>
      </div>
    </section>

    <!-- Filters and Search Section -->
    <section class="py-8 bg-white border-b">
      <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row gap-6 items-center justify-between">
          <!-- Search Bar -->
          <div class="w-full lg:w-1/3">
            <SearchBar @search="handleSearch" :loading="loading" />
          </div>

          <!-- Category Filter -->
          <div class="flex flex-wrap gap-2">
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectCategory(category.id)"
              :class="[
                'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200',
                selectedCategory === category.id
                  ? 'bg-primary-900 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              ]"
            >
              {{ category.name }} ({{ category.count }})
            </button>
          </div>

          <!-- Sort Options -->
          <div class="flex items-center gap-4">
            <select
              v-model="sortBy"
              @change="handleSortChange"
              class="input-field w-auto"
            >
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
              <option value="rating">Sort by Rating</option>
            </select>
            
            <button
              @click="toggleSortOrder"
              class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
              :title="sortOrder === 'asc' ? 'Sort Descending' : 'Sort Ascending'"
            >
              <ChevronUpIcon v-if="sortOrder === 'desc'" class="w-5 h-5" />
              <ChevronDownIcon v-else class="w-5 h-5" />
            </button>
          </div>
        </div>

        <!-- Price Range Filter -->
        <div class="mt-6 flex items-center gap-4">
          <span class="text-sm font-medium text-gray-700">Price Range:</span>
          <div class="flex items-center gap-2">
            <input
              v-model.number="priceRange[0]"
              @input="updatePriceRange"
              type="number"
              min="0"
              max="500"
              class="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
              placeholder="Min"
            >
            <span class="text-gray-500">-</span>
            <input
              v-model.number="priceRange[1]"
              @input="updatePriceRange"
              type="number"
              min="0"
              max="500"
              class="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
              placeholder="Max"
            >
            <button
              @click="resetFilters"
              class="text-sm text-primary-600 hover:text-primary-800 underline"
            >
              Reset
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Grid -->
    <section class="py-12">
      <div class="container mx-auto px-4">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-20">
          <LoadingSpinner />
        </div>

        <!-- No Products Found -->
        <div v-else-if="filteredProducts.length === 0" class="text-center py-20">
          <div class="max-w-md mx-auto">
            <ExclamationTriangleIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p class="text-gray-600 mb-6">
              Try adjusting your search criteria or browse all products.
            </p>
            <button @click="resetFilters" class="btn-primary">
              Clear Filters
            </button>
          </div>
        </div>

        <!-- Products Grid -->
        <div v-else>
          <!-- Results Info -->
          <div class="flex justify-between items-center mb-8">
            <p class="text-gray-600">
              Showing {{ filteredProducts.length }} of {{ totalProducts }} products
            </p>
          </div>

          <!-- Product Cards -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            <ProductCard
              v-for="(product, index) in filteredProducts"
              :key="product.id"
              :product="product"
              :class="`animate-fade-in-up`"
              :style="`animation-delay: ${index * 0.05}s`"
            />
          </div>

          <!-- Load More Button (for future pagination) -->
          <div class="text-center mt-12">
            <button class="btn-secondary" disabled>
              Load More Products
            </button>
            <p class="text-sm text-gray-500 mt-2">All products loaded</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import ProductCard from '@/components/ui/ProductCard.vue'
import SearchBar from '@/components/ui/SearchBar.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import { 
  ChevronUpIcon, 
  ChevronDownIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()

// Reactive data
const loading = ref(false)

// Computed properties
const categories = computed(() => productsStore.categories)
const filteredProducts = computed(() => productsStore.filteredProducts)
const totalProducts = computed(() => productsStore.totalProducts)
const selectedCategory = computed(() => productsStore.selectedCategory)
const sortBy = computed({
  get: () => productsStore.sortBy,
  set: (value) => productsStore.setSortBy(value)
})
const sortOrder = computed(() => productsStore.sortOrder)
const priceRange = computed({
  get: () => productsStore.priceRange,
  set: (value) => productsStore.setPriceRange(value)
})

const pageTitle = computed(() => {
  if (route.params.category && route.params.category !== 'all') {
    const category = categories.value.find(c => c.id === route.params.category)
    return category ? category.name : 'Products'
  }
  return 'All Products'
})

const pageDescription = computed(() => {
  if (route.params.category && route.params.category !== 'all') {
    return `Browse our collection of premium ${route.params.category}`
  }
  return 'Discover our complete collection of premium men\'s clothing'
})

// Methods
const handleSearch = async (query) => {
  loading.value = true
  await productsStore.searchProducts(query)
  loading.value = false
}

const selectCategory = (categoryId) => {
  productsStore.setSelectedCategory(categoryId)
  
  // Update URL without page reload
  if (categoryId === 'all') {
    router.push('/products')
  } else {
    router.push(`/products/${categoryId}`)
  }
}

const handleSortChange = () => {
  // Sort change is handled by the computed property
}

const toggleSortOrder = () => {
  const newOrder = sortOrder.value === 'asc' ? 'desc' : 'asc'
  productsStore.setSortOrder(newOrder)
}

const updatePriceRange = () => {
  // Price range update is handled by the computed property
}

const resetFilters = () => {
  productsStore.resetFilters()
  router.push('/products')
}

// Watch for route changes
watch(() => route.params.category, (newCategory) => {
  if (newCategory) {
    productsStore.setSelectedCategory(newCategory)
  } else {
    productsStore.setSelectedCategory('all')
  }
}, { immediate: true })

// Lifecycle
onMounted(async () => {
  loading.value = true
  
  // Set initial category from route
  if (route.params.category) {
    productsStore.setSelectedCategory(route.params.category)
  }
  
  // Fetch products (simulated)
  await productsStore.fetchProducts()
  loading.value = false
})
</script>

<style scoped>
.products-page {
  min-height: 100vh;
}
</style>
