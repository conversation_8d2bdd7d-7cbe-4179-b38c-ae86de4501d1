<template>
  <div class="product-detail-page">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <LoadingSpinner />
    </div>

    <!-- Product Not Found -->
    <div v-else-if="!product" class="flex justify-center items-center min-h-screen">
      <div class="text-center">
        <ExclamationTriangleIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Product Not Found</h2>
        <p class="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
        <router-link to="/products" class="btn-primary">
          Back to Products
        </router-link>
      </div>
    </div>

    <!-- Product Details -->
    <div v-else class="py-8">
      <!-- Breadcrumb -->
      <div class="container mx-auto px-4 mb-8">
        <nav class="flex items-center space-x-2 text-sm text-gray-600">
          <router-link to="/" class="hover:text-primary-600">Home</router-link>
          <ChevronRightIcon class="w-4 h-4" />
          <router-link to="/products" class="hover:text-primary-600">Products</router-link>
          <ChevronRightIcon class="w-4 h-4" />
          <span class="text-gray-900">{{ product.name }}</span>
        </nav>
      </div>

      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- Product Images -->
          <div class="space-y-4">
            <!-- Main Image -->
            <div class="aspect-square bg-gray-100 rounded-xl overflow-hidden">
              <img
                :src="selectedImage"
                :alt="product.name"
                class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>

            <!-- Thumbnail Images -->
            <div class="flex space-x-4 overflow-x-auto pb-2">
              <button
                v-for="(image, index) in product.images"
                :key="index"
                @click="selectedImage = image"
                :class="[
                  'flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200',
                  selectedImage === image ? 'border-primary-600' : 'border-gray-200 hover:border-gray-300'
                ]"
              >
                <img
                  :src="image"
                  :alt="`${product.name} view ${index + 1}`"
                  class="w-full h-full object-cover"
                />
              </button>
            </div>
          </div>

          <!-- Product Info -->
          <div class="space-y-6">
            <!-- Title and Price -->
            <div>
              <div class="flex items-center gap-2 mb-2">
                <span v-if="product.isNew" class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                  New
                </span>
                <span v-if="product.isFeatured" class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                  Featured
                </span>
              </div>
              
              <h1 class="text-3xl md:text-4xl font-display font-bold text-primary-900 mb-4">
                {{ product.name }}
              </h1>
              
              <div class="flex items-center gap-4 mb-4">
                <span class="text-3xl font-bold text-primary-900">
                  ${{ product.price.toFixed(2) }}
                </span>
                <span v-if="product.originalPrice > product.price" class="text-xl text-gray-500 line-through">
                  ${{ product.originalPrice.toFixed(2) }}
                </span>
                <span v-if="product.originalPrice > product.price" class="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
                  Save ${{ (product.originalPrice - product.price).toFixed(2) }}
                </span>
              </div>

              <!-- Rating -->
              <div class="flex items-center gap-2 mb-6">
                <div class="flex items-center">
                  <StarIcon
                    v-for="i in 5"
                    :key="i"
                    :class="[
                      'w-5 h-5',
                      i <= Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                    ]"
                  />
                </div>
                <span class="text-sm text-gray-600">
                  {{ product.rating }} ({{ product.reviews }} reviews)
                </span>
              </div>
            </div>

            <!-- Description -->
            <div>
              <h3 class="text-lg font-semibold text-primary-900 mb-2">Description</h3>
              <p class="text-gray-700 leading-relaxed">{{ product.description }}</p>
            </div>

            <!-- Features -->
            <div>
              <h3 class="text-lg font-semibold text-primary-900 mb-2">Features</h3>
              <ul class="space-y-1">
                <li v-for="feature in product.features" :key="feature" class="flex items-center text-gray-700">
                  <CheckIcon class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                  {{ feature }}
                </li>
              </ul>
            </div>

            <!-- Size Selection -->
            <div v-if="product.sizes && product.sizes.length > 0">
              <h3 class="text-lg font-semibold text-primary-900 mb-2">Size</h3>
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="size in product.sizes"
                  :key="size"
                  @click="selectedSize = size"
                  :class="[
                    'px-4 py-2 border rounded-lg font-medium transition-all duration-200',
                    selectedSize === size
                      ? 'border-primary-600 bg-primary-600 text-white'
                      : 'border-gray-300 text-gray-700 hover:border-primary-300'
                  ]"
                >
                  {{ size }}
                </button>
              </div>
            </div>

            <!-- Color Selection -->
            <div v-if="product.colors && product.colors.length > 0">
              <h3 class="text-lg font-semibold text-primary-900 mb-2">Color</h3>
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="color in product.colors"
                  :key="color"
                  @click="selectedColor = color"
                  :class="[
                    'px-4 py-2 border rounded-lg font-medium transition-all duration-200',
                    selectedColor === color
                      ? 'border-primary-600 bg-primary-600 text-white'
                      : 'border-gray-300 text-gray-700 hover:border-primary-300'
                  ]"
                >
                  {{ color }}
                </button>
              </div>
            </div>

            <!-- Quantity and Add to Cart -->
            <div class="space-y-4">
              <div class="flex items-center gap-4">
                <label class="text-lg font-semibold text-primary-900">Quantity:</label>
                <div class="flex items-center border border-gray-300 rounded-lg">
                  <button
                    @click="decreaseQuantity"
                    class="px-3 py-2 hover:bg-gray-100 transition-colors"
                    :disabled="quantity <= 1"
                  >
                    <MinusIcon class="w-4 h-4" />
                  </button>
                  <span class="px-4 py-2 border-x border-gray-300 min-w-[3rem] text-center">
                    {{ quantity }}
                  </span>
                  <button
                    @click="increaseQuantity"
                    class="px-3 py-2 hover:bg-gray-100 transition-colors"
                  >
                    <PlusIcon class="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div class="flex flex-col sm:flex-row gap-4">
                <button
                  @click="addToCart"
                  :disabled="!product.inStock || addingToCart"
                  class="btn-primary flex-1 flex items-center justify-center gap-2"
                >
                  <ShoppingCartIcon class="w-5 h-5" />
                  <span v-if="addingToCart">Adding...</span>
                  <span v-else-if="!product.inStock">Out of Stock</span>
                  <span v-else>Add to Cart</span>
                </button>
                
                <button
                  @click="toggleWishlist"
                  class="btn-secondary flex items-center justify-center gap-2"
                >
                  <HeartIcon :class="['w-5 h-5', isInWishlist ? 'fill-current text-red-500' : '']" />
                  Wishlist
                </button>
              </div>
            </div>

            <!-- Stock Status -->
            <div class="flex items-center gap-2">
              <div :class="[
                'w-3 h-3 rounded-full',
                product.inStock ? 'bg-green-500' : 'bg-red-500'
              ]"></div>
              <span :class="[
                'text-sm font-medium',
                product.inStock ? 'text-green-700' : 'text-red-700'
              ]">
                {{ product.inStock ? 'In Stock' : 'Out of Stock' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Related Products -->
        <div v-if="relatedProducts.length > 0" class="mt-20">
          <h2 class="text-3xl font-display font-bold text-primary-900 mb-8 text-center">
            Related Products
          </h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <ProductCard
              v-for="relatedProduct in relatedProducts"
              :key="relatedProduct.id"
              :product="relatedProduct"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProductsStore } from '@/stores/products'
import { useCartStore } from '@/stores/cart'
import ProductCard from '@/components/ui/ProductCard.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import {
  ChevronRightIcon,
  StarIcon,
  CheckIcon,
  MinusIcon,
  PlusIcon,
  ShoppingCartIcon,
  HeartIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

const route = useRoute()
const router = useRouter()
const productsStore = useProductsStore()
const cartStore = useCartStore()

// Props
const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  }
})

// Reactive data
const loading = ref(false)
const selectedImage = ref('')
const selectedSize = ref('')
const selectedColor = ref('')
const quantity = ref(1)
const addingToCart = ref(false)
const isInWishlist = ref(false)

// Computed properties
const product = computed(() => productsStore.currentProduct)
const relatedProducts = computed(() => {
  if (!product.value) return []
  return productsStore.relatedProducts(product.value.id, product.value.category)
})

// Methods
const increaseQuantity = () => {
  quantity.value++
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const addToCart = async () => {
  if (!product.value || !product.value.inStock) return
  
  addingToCart.value = true
  
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    cartStore.addItem(
      product.value,
      selectedSize.value,
      selectedColor.value,
      quantity.value
    )
    
    // Show success message or animation
    // You could add a toast notification here
    
  } catch (error) {
    console.error('Failed to add to cart:', error)
  } finally {
    addingToCart.value = false
  }
}

const toggleWishlist = () => {
  isInWishlist.value = !isInWishlist.value
  // Here you would typically save to wishlist store or API
}

// Watch for route changes
watch(() => route.params.id, async (newId) => {
  if (newId) {
    await loadProduct(newId)
  }
}, { immediate: true })

// Load product data
const loadProduct = async (productId) => {
  loading.value = true
  
  try {
    await productsStore.fetchProductById(productId)
    
    if (product.value) {
      selectedImage.value = product.value.image
      
      // Set default selections
      if (product.value.sizes && product.value.sizes.length > 0) {
        selectedSize.value = product.value.sizes[0]
      }
      if (product.value.colors && product.value.colors.length > 0) {
        selectedColor.value = product.value.colors[0]
      }
    }
  } catch (error) {
    console.error('Failed to load product:', error)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadProduct(props.id)
})
</script>

<style scoped>
.product-detail-page {
  min-height: 100vh;
}
</style>
