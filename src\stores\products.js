import { defineStore } from 'pinia'
import { products, categories, featuredProducts, newProducts } from '@/data/products.js'

export const useProductsStore = defineStore('products', {
  state: () => ({
    products: products,
    categories: categories,
    featuredProducts: featuredProducts,
    newProducts: newProducts,
    currentProduct: null,
    searchQuery: '',
    selectedCategory: 'all',
    sortBy: 'name',
    sortOrder: 'asc',
    priceRange: [0, 500],
    loading: false
  }),

  getters: {
    filteredProducts: (state) => {
      let filtered = [...state.products]

      // Filter by search query
      if (state.searchQuery) {
        filtered = filtered.filter(product =>
          product.name.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
          product.description.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(state.searchQuery.toLowerCase())
        )
      }

      // Filter by category
      if (state.selectedCategory !== 'all') {
        filtered = filtered.filter(product => product.category === state.selectedCategory)
      }

      // Filter by price range
      filtered = filtered.filter(product =>
        product.price >= state.priceRange[0] && product.price <= state.priceRange[1]
      )

      // Sort products
      filtered.sort((a, b) => {
        let aValue, bValue

        switch (state.sortBy) {
          case 'price':
            aValue = a.price
            bValue = b.price
            break
          case 'rating':
            aValue = a.rating
            bValue = b.rating
            break
          case 'name':
          default:
            aValue = a.name.toLowerCase()
            bValue = b.name.toLowerCase()
            break
        }

        if (state.sortOrder === 'desc') {
          return aValue < bValue ? 1 : -1
        } else {
          return aValue > bValue ? 1 : -1
        }
      })

      return filtered
    },

    productById: (state) => {
      return (id) => state.products.find(product => product.id === parseInt(id))
    },

    relatedProducts: (state) => {
      return (productId, category) => {
        return state.products
          .filter(product => product.id !== productId && product.category === category)
          .slice(0, 4)
      }
    },

    totalProducts: (state) => state.filteredProducts.length,

    hasProducts: (state) => state.products.length > 0,

    isLoading: (state) => state.loading
  },

  actions: {
    setSearchQuery(query) {
      this.searchQuery = query
    },

    setSelectedCategory(category) {
      this.selectedCategory = category
    },

    setSortBy(sortBy) {
      this.sortBy = sortBy
    },

    setSortOrder(order) {
      this.sortOrder = order
    },

    setPriceRange(range) {
      this.priceRange = range
    },

    setCurrentProduct(productId) {
      this.currentProduct = this.productById(productId)
    },

    clearCurrentProduct() {
      this.currentProduct = null
    },

    setLoading(loading) {
      this.loading = loading
    },

    resetFilters() {
      this.searchQuery = ''
      this.selectedCategory = 'all'
      this.sortBy = 'name'
      this.sortOrder = 'asc'
      this.priceRange = [0, 500]
    },

    // Simulate API calls
    async fetchProducts() {
      this.setLoading(true)
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      this.setLoading(false)
      return this.products
    },

    async fetchProductById(id) {
      this.setLoading(true)
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))
      const product = this.productById(id)
      this.currentProduct = product
      this.setLoading(false)
      return product
    },

    async searchProducts(query) {
      this.setLoading(true)
      this.setSearchQuery(query)
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800))
      this.setLoading(false)
      return this.filteredProducts
    }
  }
})
